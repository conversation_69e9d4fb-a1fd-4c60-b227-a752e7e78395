import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';

export default defineConfig({
  plugins: [
    react(),
    dts({
      insertTypesEntry: true,
    }),
  ],
  build: {
    lib: {
      entry: 'packages/components/index.ts',
      name: 'RuyiComponents',
      fileName: 'index',
    },
    rollupOptions: {
      external: [
        'react', 
        'react-dom',
      ],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
        dir: 'packages/components/dist', // 指定输出目录
      },
    },
  },
});