import type { StorybookConfig } from '@storybook/react-vite';
import path from 'path';
import tsconfigPaths from "vite-tsconfig-paths";

const config: StorybookConfig = {
  "stories": [
    "../src/**/*.mdx",
    "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"
  ],
  "addons": [
    "@storybook/addon-onboarding",
    "@chromatic-com/storybook",
    "@storybook/addon-docs",
    "@storybook/addon-a11y",
    "@storybook/addon-vitest"
  ],
  "framework": {
    "name": "@storybook/react-vite",
    "options": {}
  },
  // 使用 Vite 构建器
  core: {
    builder: '@storybook/builder-vite',
  },
  // 配置 Vite
  viteFinal: (config) => {
    // 添加 tsconfigPaths 插件
    config.plugins = [...(config.plugins || []), tsconfigPaths()];

    return {
      ...config,
      resolve: {
        ...config.resolve,
        alias: {
          ...(config.resolve?.alias || {}),
        },
      },
    };
  },
};
export default config;
