import { Meta, <PERSON>s, Story, Canvas } from '@storybook/addon-docs/blocks';
 
import * as ButtonStories from './Button.stories';
 
<Meta title="Button" of={ButtonStories } />
 
# Definition
 
Button is a clickable interactive element that triggers a response.
 
You can place text and icons inside of a button.
 
Buttons are often used for form submissions and to toggle elements into view.
 
## Usage
 
The component comes in different variants such as `primary`, `secondary`, `large` and `small` which you can use to alter the look and feel of the button.
 
## Inputs
 
Button has the following properties:
 
<Controls />

# Stories

## Primary

<Canvas of={ButtonStories.Primary} />

## Secondary

<Canvas of={ButtonStories.Secondary} />

## Large

<Canvas of={ButtonStories.Large} />

## Medium

<Canvas of={ButtonStories.Medium} />

## Small

<Canvas of={ButtonStories.Small} />
