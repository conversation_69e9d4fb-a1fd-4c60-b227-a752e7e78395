import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';

// import { fn } from 'storybook/test';

import CrystalBall from '@ruyi-components/components/CrystalBall';

import { CrystalBallData } from './data';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'Example/水晶球',
  component: CrystalBall,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    data: {
      description: '人群数据',
      control: { type: 'object' },
    },
    width: {
      description: 'SVG 宽度',
      control: { type: 'number', min: 400, max: 1200, step: 10 },
      defaultValue: 800,
    },
    svgId: {
      description: 'SVG 元素的 ID',
      control: { type: 'text' },
      defaultValue: 'crystalBall',
    },
    dataKey: {
      description: '数据键值',
      control: { type: 'select', options: ['tgi', 'ta'] },
      defaultValue: 'tgi',
    },
    EmptyComponent: {
      description: '无数据时的占位内容',
      control: { type: 'text' },
      defaultValue: '暂无数据',
    },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {},
} satisfies Meta<typeof CrystalBall>;

export default meta;

type Story = StoryObj<typeof meta>;

export const WithData: Story = {
  args: {
    title: '香水核心消费人群',
    data: CrystalBallData,
  },
  name: '展示TGI'
};
export const WithData2: Story = {
  args: {
    title: '香水核心消费人群',
    svgId: 'crystalBall2',
    data: CrystalBallData,
  },
  name: '展示TA'
};

export const NoData: Story = {
  args: {
    svgId: 'crystalBall2',
  },
  name: '无数据',
};

export const TooltipEnterable: Story = {
  args: {
    title: '香水核心消费人群',
    svgId: 'crystalBall3',
    tooltipEnterable: true,
    data: CrystalBallData,
  },
  name: '可交互的Tooltip'
};
