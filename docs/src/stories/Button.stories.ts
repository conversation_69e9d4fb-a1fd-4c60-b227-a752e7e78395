import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';

import { fn } from 'storybook/test';

import Button from '../../../packages/components/src/Button';
// import Button from '@ruyi-components/components/Button';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'Example/Button',
  component: Button,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: fn() },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary: Story = {
  args: {
    theme: 'primary',
    label: 'Button',
  },
};

export const Secondary: Story = {
  args: {
    theme: 'secondary',
    label: 'Button',
  },
};

export const Large: Story = {
  args: {
    theme: 'primary',
    label: 'Button',
    size: 'large',
  },
};

export const Medium: Story = {
  args: {
    theme: 'primary',
    label: 'Button',
    size: 'medium',
  },
};

export const Small: Story = {
  args: {
    theme: 'primary',
    label: 'Button',
    size: 'small',
  },
};
