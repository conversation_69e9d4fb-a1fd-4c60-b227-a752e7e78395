{"name": "docs", "private": true, "version": "0.0.0", "type": "module", "scripts": {"storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@ruyi-components/components": "workspace:*", "@ruyi-components/utils": "workspace:*", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0", "@eslint/js": "^9.25.0", "@storybook/addon-a11y": "^9.0.6", "@storybook/addon-docs": "^9.0.6", "@storybook/addon-onboarding": "^9.0.6", "@storybook/addon-vitest": "^9.0.6", "@storybook/addons": "^7.6.17", "@storybook/react-vite": "^9.0.6", "@storybook/theming": "^8.6.14", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.2.2", "@vitest/coverage-v8": "^3.2.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "^9.0.6", "globals": "^16.0.0", "playwright": "^1.52.0", "storybook": "^9.0.6", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-alias": "^0.1.1", "vitest": "^3.2.2"}}