{"name": "ruyi-components", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "vite build", "clean": "rimraf packages/components/dist packages/utils/dist dist"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "devDependencies": {"@types/node": "^22.15.30", "@types/react": "^16.8.0", "@vitejs/plugin-react": "^4.5.1", "less": "^4.3.0", "rimraf": "^6.0.1", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vite-tsconfig-paths": "^5.1.4"}, "dependencies": {"react": "^17.0.0", "react-dom": "^17.0.0", "ruyi-components": "workspace:*"}}