import React, { CSSProperties } from "react";
import styles from "./Button.module.less";
import clsx from "clsx";

type Size = "small" | "medium" | "large";
type Theme = "primary" | "secondary";

export interface ButtonProps {
  label: string;
  onClick?: () => void;
  theme?: Theme
  size?: Size;
  className?: string;
  style?: CSSProperties;
}
const sizeClassNameMap: Record<Size, string> = {
  small: styles["btn-small"],
  medium: styles["btn-medium"],
  large: styles["btn-large"],
};

const themeClassNameMap: Record<Theme, string>  = {
  primary: styles["btn-primary"],
  secondary: styles["btn-secondary"],
};

const Button: React.FC<ButtonProps> = (props) => {
  const { label, onClick, theme, size, className, style } = props;
  const sizeClassName = sizeClassNameMap[size || "small"];
  const themeClassName = themeClassNameMap[theme || "primary"];
  return (
    <button
      className={clsx(styles.btn, sizeClassName, themeClassName, className)}
      onClick={onClick}
      style={style}
    >
      {label}
    </button>
  );
};

Button.defaultProps = {
  size: 'small',
};

export default Button;
