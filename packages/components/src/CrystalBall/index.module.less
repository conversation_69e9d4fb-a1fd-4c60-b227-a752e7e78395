.container {
  padding: 24px;
}
.svgContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  text-align: center;
}

@transition-duration: 400ms;
@transition-duration-s: 300ms;
@transition-duration-xs: 150ms;

.svgBgMask {
  transition: opacity @transition-duration;
}
.svgSectorTitle {
  transition: fill @transition-duration;
  fill: var(--fill);
}
.svgSectorTitleBG {
  transition: opacity @transition-duration;
  opacity: 0;
  fill: var(--fill);
  filter: drop-shadow(0px 4px 12px rgba(#04369a, 0.25));
}
.svgSectorGroup {
  transition: opacity @transition-duration;

  &Active {
    .svgSectorTitle {
      fill: white;
    }
    .svgSectorTitleBG {
      opacity: 1;
    }
  }
}
.svgSectorUnderGroup {
  transition: opacity @transition-duration;
}
.svgPointGroup {
  font-weight: 400;
  cursor: pointer;
  // transition: font-weight @transition-duration-xs;
  &Active {
    font-weight: 600;
  }
}

.tooltip {
  position: fixed;
  left: 0;
  top: 0;
  pointer-events: none;
  transition: opacity @transition-duration-s;

  //   width: 286px;
  //   position: absolute;
  //
  font-size: 12px;

  &Enter {
    opacity: 0;
  }
  &EnterActive {
    opacity: 1;
  }
  &Leave {
    // opacity: 0;
  }
  &LeaveActive {
    opacity: 0;
  }
}

.tooltipEnterableTrigger {
  position: absolute;
  top: calc(var(--trigger-margin) * -1px);
  left: calc(var(--trigger-margin) * -1px);
  bottom: calc(var(--trigger-margin) * -1px);
  right: calc(var(--trigger-margin) * -1px);
}

.tooltipContent {
  position: relative;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.05),
    0px 6px 15px 0px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
}

.tooltipEnterable {
  .tooltipContent,
  .tooltipEnterableTrigger {
    pointer-events: all;
  }

  &.tooltipContentEntered {
    .tooltipEnterableTrigger {
      pointer-events: none;
    }
  }
}

.tooltipTitle {
  font-weight: 600;
  margin: 0;
}
.tooltipDataRow {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  margin-bottom: 0;
}
.tooltipRowName {
  max-width: 200px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.tooltipData {
  font-weight: 600;
}
