import React, { FC, useEffect, useRef, ReactNode } from 'react';
import * as d3 from 'd3';
import { useSetState } from 'ahooks';
import { debounce, get } from 'lodash-es';
import { nanoid } from 'nanoid';
import numeral from 'numeral';
import styles from './index.module.less';
import { CloudGrapAudience, CloudGraphChildItem, CloudGraphItem } from './type';
import { calculateStringLength, customShuffle, groupDataByDistribute } from './helper';
import { Tooltip, TooltipCtrlRefValue, TooltipRenderOptions } from './Tooltip';

export interface CrystalBallProps {
  title?: string;
  data?: any[];
  /**
   * 水晶球宽度
   */
  width?: number;
  /**
   * 水晶球 SVG id
   */
  svgId?: string;
  /**
   * 每层分布个数
   */
  distribute?: number[];
  /**
   * 无数据时展示的组件
   */
  EmptyComponent?: ReactNode;
  /**
   * 原始数据字段映射
   */
  fieldNames?: {
    labelFieldName?: string;
    valueFieldName?: string;
  };
  /**
   * 格式化 tooltip title
   */
  formatTooltipTitle?: (name: string) => ReactNode;
  /**
   * 格式化 tooltip 数值
   */
  formatValue?: (name: number) => ReactNode;
  /**
   * 渲染 tooltip 面板
   *
   * options:
   *  - hide(): 隐藏tooltip
   */
  renderTooltip?: (
    data: Partial<CloudGraphChildItem>[],
    options: TooltipRenderOptions
  ) => ReactNode;
  /**
   * 是否开启tooltip
   */
  tooltipEnabled?: boolean;

  /**
   * 是否可以鼠标移入操作
   */
  tooltipEnterable?: boolean;
}

interface CrystalBallState {
  show: boolean;
  loading: boolean;
  detailData: Partial<CloudGrapAudience>;
  current: Partial<CloudGraphChildItem> | null;
  currentGroup: CloudGraphItem | null;
}

const getFillColor = (layer: number) => {
  if (layer === 0) {
    return '#3477FF';
  }
  if (layer === 1) {
    return '#6396FF';
  }
  return '#7BA7FF';
};
const getTextColor = (layer: number) => {
  if (layer === 0) {
    return 'rgba(0, 0, 0, 0.95)';
  }
  if (layer === 1) {
    return 'rgba(38, 39, 41, 0.85)';
  }
  return 'rgba(38, 39, 41, 0.85)';
};

const radToDeg = (v: number) => (v / Math.PI) * 180;

const tooltipOffset = 16;

const CrystalBall: FC<CrystalBallProps> = (props) => {
  const tooltipCtrlRef = useRef<TooltipCtrlRefValue>(null);

  const {
    data,
    width = 800,
    svgId = `crystalBall-${nanoid()}`,
    distribute = [2, 3, 4],
    EmptyComponent = "暂无数据",
    formatTooltipTitle,
    formatValue,
    renderTooltip,
    tooltipEnabled,
    tooltipEnterable,
    title,
    fieldNames,
  } = props;
  const labelFieldName = get(fieldNames, 'labelFieldName', 'name');
  const valueFieldName = get(fieldNames, 'valueFieldName', 'value');

  // 需要触发渲染的状态数据
  const [state, setState] = useSetState<CrystalBallState>({
    show: false,
    loading: false,
    current: null,
    detailData: {
      audienceName: '',
      cloudGraphs: [],
    },
    currentGroup: null,
  });
  // 不触发渲染的状态数据
  const stateRef = useRef<{
    sectorEntered: boolean,
    tooltipEntered: boolean;
    enteredSectorEl: SVGGElement | null;
    enteredItemEl: SVGGElement | null;
    // 在enter到tooltip时，需要固定当前高亮的sector / item
    // 因此要禁用leave事件，直到leave tooltip时才触发，
    // 这里保存下来供后续使用
    sectorLeaveFunc: (() => void) | null;
    itemLeaveFunc: (() => void) | null;
  }>({
    sectorEntered: false,
    tooltipEntered: false,
    enteredSectorEl: null,
    enteredItemEl: null,
    sectorLeaveFunc: null,
    itemLeaveFunc: null,
  })

  const { detailData, current, currentGroup } = state;
  const renderName = (name: string) => {
    if (formatTooltipTitle) {
      return formatTooltipTitle(name);
    }
    return (
      <>
        {name}
        {valueFieldName === "tgi" && " TGI"}
        {valueFieldName === "ta" && " TA%"}
      </>
    );
  };
  const renderValue = (value: any) => {
    if (formatValue) {
      return formatValue(value);
    }
    if (valueFieldName === 'tgi') {
      return numeral(value * 100).format('0,0')
    }
    if (valueFieldName === 'ta') {
      return numeral(value).format('0,0.00%')
    }
    return value;
  };
  useEffect(() => {
    if (data) {
      setState({
        detailData: {
          audienceName: title,
          cloudGraphs: data,
        },
      });
    }
  }, [data]);

  useEffect(() => {
    if (!detailData.cloudGraphs?.length) return;
    const cloudGraphData = detailData.cloudGraphs;
    const labels = cloudGraphData.map((item) => item.graphClassName);

    const svg = d3.select(`#${svgId}`);

    const outerCircleLineColor = '#cddeff';
    const sectorLineGradient = [
      { offset: 0, color: '#3B7DFF' },
      { offset: 0.333, color: '#71A1FF' },
      { offset: 1, color: '#D6E4FF' },
    ];
    const textColor = '#296bef';
    const bgColor = '#FFF';
    const crowdRadius = 6.5;
    const R0CrowdRadius = 64;
    const centerX = width / 2;
    const centerY = width / 2;
    // 圆形背景半径（从外到内）
    const circleBGRadius = [946 / 878, 1, 540 / 878, 314 / 878];
    // 圆形背景填充（从外到内）
    const circleBGGradients = [
      [
        { offset: 0.3, color: 'rgba(115, 162, 255, 0.24)' },
        { offset: 1, color: 'rgba(255,255,255,0.05)' },
      ],
      [
        { offset: 0, color: 'rgba(115, 162, 255, 0.1)' },
        { offset: 1, color: 'rgba(255,255,255,1)' },
      ],
      [
        { offset: 0.1, color: 'rgba(115, 162, 255, 0.4)' },
        { offset: 1, color: 'rgba(255,255,255,1)' },
      ],
      [
        { offset: 0.2, color: 'rgba(115, 162, 255, 0.2)' },
        { offset: 1, color: 'rgba(255,255,255,1)' },
      ],
    ];
    // 顶点分布的层级半径（相对最大半径的比值）
    const pointLayers = [0.2, 0.44, 0.69];
    const sectorLabelRadius = 0.95;
    // 设置SVG背景颜色
    svg.style('background-color', bgColor);

    // 背景group，用于整体控制高亮
    const bgGroup = svg.append('g');

    // 定义各层级圆形背景
    const defsSelection = svg.append('defs');
    const gradientId = 'gradientId';
    let gradientIdSuffix = 0;
    const defineGradient = (
      points: { offset: number; color: string; opacity?: number }[],
      type: 'linear' | 'radial' = 'radial'
    ) => {
      const gradient = defsSelection
        .append(type === 'radial' ? 'radialGradient' : 'linearGradient')
        .attr('id', `${gradientId}-${gradientIdSuffix}`);

      gradientIdSuffix += 1;

      points.forEach(({ offset, color, opacity }) => {
        gradient
          .append('stop')
          .attr('offset', `${offset * 100}%`)
          .attr('stop-color', color)
          .attr('stop-opacity', `${opacity ?? 1}`);
      });

      return gradient;
    };
    const createCircle = (radius: number, fillUrl: string, opacity = 1, strokeColor = 'none') => {
      bgGroup
        .append('circle')
        .attr('cx', centerX)
        .attr('cy', centerY)
        .attr('r', radius)
        .attr('opacity', opacity)
        .style('stroke', strokeColor)
        .style('fill', fillUrl);
    };
    // 从大到小绘制圆形背景
    circleBGRadius
      .forEach((radius, index) => {
        const gradient = circleBGGradients[index];
        if (index !== 0) {
          // 根据设计，除了最外层，蓝色渐变层下会垫着一层纯白色
          createCircle((radius * width) / 2, 'white', 1);
        }
        createCircle((radius * width) / 2, `url(#${defineGradient(gradient).attr('id')})`, 0.8);
      });

    // 添加最外层圆
    bgGroup
      .append('circle')
      .attr('cx', centerX)
      .attr('cy', centerY)
      .attr('aria-label', '最外层圆')
      .attr('aria-hidden', true)
      .attr('r', width / 2)
      .style('stroke', outerCircleLineColor)
      .style('fill', 'none');

    // 获取扇形区域 path 的 d 值
    const getSectorPathStr = (option: {
      r: number;
      innerR?: number;
      startAngle: number;
      angle: number;
      roundCorner?: number;
    }) => {
      const { r, innerR, startAngle, angle, roundCorner } = option;
      const arc = d3
        .arc()
        .innerRadius(innerR ?? 0)
        .outerRadius(r)
        .startAngle(startAngle + Math.PI / 2)
        .endAngle(startAngle + angle + Math.PI / 2)
        .cornerRadius(roundCorner ?? 0);
      return arc();
    };
    // 创建扇形区域path
    const createSectorPath = (
      option: {
        parent: any;
        center?: [number, number];
      } & Parameters<typeof getSectorPathStr>[0]
    ) => {
      const { parent, center = [0, 0], ...rest } = option;
      return parent
        .append('path')
        .attr('d', getSectorPathStr(rest))
        .attr('transform', `translate(${center[0]}, ${center[1]})`);
    };
    // 背景色蒙层扇形，用于
    const bgMask = createSectorPath({
      parent: bgGroup,
      center: [width / 2, width / 2],
      r: width / 2,
      startAngle: Math.PI / 2,
      angle: Math.PI * 2 - Math.PI / 2,
    })
      .attr('fill', 'rgba(255,255,255,0.7)')
      .attr('opacity', '0')
      .attr('class', styles.svgBgMask);

    const sectorLineGradientDef = defineGradient(sectorLineGradient, 'linear');

    // 扇区中某些元素必须在内容以下（如分割线），统一放在一个group中控制
    // 因单个扇区内容会动态根据hover调整到最上层，而装饰元素总是在最下，所以分开控制
    const sectorUnderGroup = svg.append('g').attr('aria-label', '扇区下层装饰')
      .attr('aria-hidden', true);
    const sectorUnderGroupItems: any[] = [];
    // 扇区内容group，如文字、顶点等，在装饰元素之上
    const sectorGroup = svg.append('g').attr('aria-label', '扇区')
      .attr('aria-hidden', true);
    // 记录各个扇区的<g/>元素，用于统一控制hover等状态
    const sectorGroupList: any[] = [];
    cloudGraphData.forEach((graphData, i) => {
      const angle = (i / labels.length) * 2 * Math.PI /* 从正上方开始，逆时针旋转90度 */ - Math.PI / 2;
      const startAngle = angle - Math.PI / labels.length;
      const angleDelta = (Math.PI / labels.length) * 2;

      // 扇形分割线2条，放在内容以下的部分
      const sectorUnderGroupItem = sectorUnderGroup.append('g')
        .attr('class', styles.svgSectorUnderGroup);
      sectorUnderGroupItem
        .append('rect')
        .attr('x', width / 2)
        .attr('y', width / 2)
        .attr('width', width / 2)
        .attr('height', 1)
        .attr('transform', `rotate(${radToDeg(angle - Math.PI / labels.length)})`)
        .attr('transform-origin', 'center')
        .style('fill', `url(#${sectorLineGradientDef.attr('id')})`);
      sectorUnderGroupItem
        .append('rect')
        .attr('x', width / 2)
        .attr('y', width / 2)
        .attr('width', width / 2)
        .attr('height', 1)
        .attr('transform', `rotate(${radToDeg(angle + Math.PI / labels.length)})`)
        .attr('transform-origin', 'center')
        .style('fill', `url(#${sectorLineGradientDef.attr('id')})`)
        .style('filter', 'drop-shadow(1px 1px 0 #EAF1FF)');
      sectorUnderGroupItems.push(sectorUnderGroupItem);

      // 将每一个扇区放在group中统一控制
      const sectorGroupItem = sectorGroup
        .append('g')
        .attr('class', styles.svgSectorGroup)
        .on('pointerenter', (event: any) => {
          stateRef.current.sectorEntered = true;
          stateRef.current.enteredSectorEl = sectorGroup.node();

          setState({
            currentGroup: graphData,
            current: null
          });
          // 将当前区域动态调整到最上层
          // 避免此时hover到其他扇区溢出的文本标签，造成高亮不稳定的问题
          const currentSectorGroupEl = sectorGroupList[i].node();
          sectorGroup.node()?.append(currentSectorGroupEl);
          // 控制背景颜色遮罩
          bgMask
            .attr(
              'd',
              getSectorPathStr({
                r: width / 2,
                startAngle: startAngle + angleDelta,
                angle: Math.PI * 2 - angleDelta,
              })
            )
            .attr('opacity', '1');
          // 控制其他扇区变淡
          sectorGroupList.forEach((sector, index) => {
            if (index === i) {
              sector.attr('opacity', 1);
              return;
            }
            sector.attr('opacity', 0.3);
          });
          sectorUnderGroupItems.forEach((sector, index) => {
            if (index === i) {
              sector.attr('opacity', 1);
              return;
            }
            sector.attr('opacity', 0.3);
          });
          // 当前扇区添加高亮样式
          currentSectorGroupEl.classList.add(styles.svgSectorGroupActive);

          tooltipCtrlRef.current?.show({ x: event.clientX, y: event.clientY });
        })
        // 添加鼠标移出事件
        .on('pointerleave', () => {
          // 保存leave事件，判断是否为tooltipEnterable的情况
          // 动态确定是此时调用还是tooltip的onLeave时调用
          const leaveFunc = () => {
            stateRef.current.sectorEntered = false;

            bgMask.attr('opacity', '0');
            sectorGroupList.forEach((sector, index) => {
              if (index === i) {
                return;
              }
              sector.attr('opacity', 1);
            });
            sectorUnderGroupItems.forEach((sector, index) => {
              if (index === i) {
                return;
              }
              sector.attr('opacity', 1);
            });
            // 当前扇区去除高亮样式
            sectorGroupList[i].node().classList.remove(styles.svgSectorGroupActive);

            tooltipCtrlRef.current?.hide();
          }

          // 延迟到tooltip onLeave时调用
          if (
            stateRef.current.tooltipEntered &&
            stateRef.current.enteredSectorEl === sectorGroup.node()
          ) {
            stateRef.current.sectorLeaveFunc = leaveFunc;
            return;
          }

          // 其他情况, 直接调用
          leaveFunc();
          stateRef.current.sectorLeaveFunc = null;
        })
        .on('pointermove', (event) => {
          tooltipCtrlRef.current?.update({ x: event.clientX, y: event.clientY });
          // handlePointerMove(event, graphData);
        });

      sectorGroupList.push(sectorGroupItem);

      // 绘制扇形鼠标响应区域
      createSectorPath({
        parent: sectorGroupItem,
        r: width / 2,
        center: [width / 2, width / 2],
        startAngle,
        angle: angleDelta,
      }).attr('fill', 'transparent');
      // 添加扇形标题 start
      const createTextNode = () => {
        // 下半个圆，扇形标题文本按自身x轴翻转180度
        const filpped = angle > 0 && angle < Math.PI;
        return sectorGroupItem
          .append('text')
          .attr(
            'transform',
            `translate(${centerX + (sectorLabelRadius * Math.cos(angle) * width) / 2}, ${centerY + (sectorLabelRadius * Math.sin(angle) * width) / 2
            }) rotate(${radToDeg(filpped ? angle - Math.PI / 2 : angle + Math.PI / 2)})`
          )
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'central')
          .attr('class', styles.svgSectorTitle)
          .style('font-size', '14px')
          .style('font-weight', 600)
          .style('--fill', textColor)
          .text(graphData.graphClassName)
          .node();
      };

      // 计算角度偏差，让出1px边缘，使得扇形标题的hover背景不要覆盖分割线
      // 1 / (圆周) = anglePad / 360
      // 1 / (2πr) = anglePad / 2π, anglePad = 1 / r
      const anglePad = 1 / (width / 2);
      // 添加扇形标题的hover背景
      createSectorPath({
        parent: sectorGroupItem,
        center: [width / 2, width / 2],
        r: width / 2,
        innerR: (width / 2) * 0.9,
        startAngle: startAngle + anglePad,
        angle: angleDelta - anglePad,
        roundCorner: 5,
      })
        .style('--fill', textColor)
        .attr('class', styles.svgSectorTitleBG);

      createTextNode();
      // 添加扇形标题 end

      const groupedData = groupDataByDistribute(
        graphData.list.map((item) => ({
          ...item,
          _value: item[valueFieldName] !== 'number' ? parseFloat(item[valueFieldName]) : item[valueFieldName],
        })),
        (item) => item._value,
        distribute
      );
      const shuffledGroup = groupedData.map((item, index) =>
        customShuffle(item, `${detailData.audienceName}${graphData.graphClassName}${index}`)
      );

      shuffledGroup.forEach((layer, layerIndex) => {
        const layerCount = distribute[layerIndex];
        if (!layerCount) {
          return;
        }

        // 为保证相邻扇形间顶点距离一致，边缘顶点到扇形边缘的间隔角度，
        // 需要为扇形内相邻顶点间隔角度的一半。以`o`为顶点`-`为角度间隔，对于3个顶点，形如：
        // |-o--o--o-|-o--o--o-|
        // 此时间隔为 1 / (0.5+1+1+0.5) = 1 / 3 = 1 / count
        const layerAngleDelta = angleDelta / layerCount;
        const layerStartAngle = startAngle + 0.5 * layerAngleDelta;
        const radius = (pointLayers[layerIndex] * width) / 2;
        layer.forEach((point, index) => {
          if (!point) {
            return;
          }
          const angle = layerStartAngle + layerAngleDelta * index;

          let hasAddedInteractiveArea = false;
          const pointGroup = sectorGroupItem
            .append('g')
            .attr('class', styles.svgPointGroup)
            .on('pointerenter', (e) => {
              stateRef.current.enteredItemEl = pointGroup.node();

              setState({
                current: point,
              });

              pointGroup.node()?.classList.add(styles.svgPointGroupActive);
              // 在用户鼠标移入进行交互时，才按需创建交互区域
              // 避免初始化时大量调用getBBox的性能问题
              // 交互区域用来防止鼠标hover到group中的空白部分触发 pointerleave
              // 以及适当扩大交互范围
              if (!hasAddedInteractiveArea) {
                const pad = 2; // 扩大的边距
                const nodeBox = pointGroup.node()?.getBBox();
                pointGroup
                  .append('rect')
                  .attr('x', nodeBox.x - pad)
                  .attr('y', nodeBox.y - pad)
                  .attr('width', nodeBox.width + 2 * pad)
                  .attr('height', nodeBox.height + 2 * pad)
                  .attr('fill', 'transparent')
                  .on('pointermove', (args) => {

                    tooltipCtrlRef.current?.update({ x: args.pageX, y: args.clientY })
                  });
                hasAddedInteractiveArea = true;
              }

              tooltipCtrlRef.current?.show({ x: e.pageX, y: e.clientY })
            })
            // 添加鼠标移出事件
            .on('pointerleave', (e) => {
              const leaveFunc = () => {
                pointGroup.node()?.classList.remove(styles.svgPointGroupActive);
                setState({
                  current: null,
                });

                if (!stateRef.current.sectorEntered) {
                  tooltipCtrlRef.current?.hide();
                }
              }

              if (
                stateRef.current.tooltipEntered &&
                stateRef.current.enteredItemEl === pointGroup.node()
              ) {
                stateRef.current.itemLeaveFunc = leaveFunc;
                return;
              }

              leaveFunc();
              stateRef.current.itemLeaveFunc = null;
            });
          // 绘制人群圆形
          pointGroup
            .append('circle')
            .attr('cx', centerX + radius * Math.cos(angle))
            .attr('cy', centerY + radius * Math.sin(angle))
            .attr('r', crowdRadius)
            .attr('class', 'circle')
            .style('fill', getFillColor(layerIndex))
            .style('opacity', 1);

          // 绘制人群名称
          // 判断是否为右侧点，翻转标签位置到顶点右边
          const isRightHalf = angle > -Math.PI / 2 && angle <= Math.PI / 2;

          pointGroup
            .append('text')
            .attr('x', centerX + radius * Math.cos(angle) + (isRightHalf ? 1 : -1) * (crowdRadius + 6))
            .attr('y', centerY + radius * Math.sin(angle) + 1)
            .attr('text-anchor', isRightHalf ? 'start' : 'end')
            .attr('dominant-baseline', 'middle')
            .style('font-size', '13px')
            .style('fill', getTextColor(layerIndex))
            .text(calculateStringLength(point[labelFieldName]) > 12 ? `${point[labelFieldName].slice(0, 5)}…` : point[labelFieldName]);
        });
      });
    });
    // 扇形区域以上的部分，放在topGroup中，
    // 作为调整扇形区域前后关系的基准（层级不高于该group）
    const topGroup = svg.append('g');
    // 中心标题圆圈
    topGroup
      .append('circle')
      .attr('cx', centerX)
      .attr('cy', centerY)
      .attr('r', R0CrowdRadius)
      .attr('name', '智能R0中心')
      .style('fill', '#006DF8')
      .style('filter', 'drop-shadow(0 0 16px rgba(4, 54, 154, 0.25))');

    // 绘制居中的标题
    const centerTitle = detailData.audienceName || ''; // 标题
    const titleSplitArr = []; // 数组
    let i = 0;
    while (i < centerTitle.length) {
      titleSplitArr.push(centerTitle.slice(i, i + 4));
      i += 4;
    }

    const titleLineHight = 22;
    const titleTextNode = topGroup
      .append('text')
      .attr('x', centerX)
      .attr('y', centerY + 1.2)
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .style('font-size', '16px')
      .style('line-height', `${titleLineHight}px`)
      .style('fill', '#FFF')
      .style('font-weight', 'bold');
    for (let i = 0; i < titleSplitArr.length; i++) {
      const str = titleSplitArr[i];
      const dy = `${i === 0 ? (titleSplitArr.length - 1) * (-titleLineHight / 2) : titleLineHight}px`;
      titleTextNode
        .append('tspan') // 添加 tspan 元素
        .attr('x', centerX) // 保持 x 坐标
        .attr('dy', dy) // 设置 y 偏移以换行
        .text(str); // 添加文本
    }
  }, [detailData.cloudGraphs]);

  const tooltipDataList = current ? [current] : currentGroup?.list || [];

  const renderTooltipElement = (options: TooltipRenderOptions) => {
    if (renderTooltip) {
      return renderTooltip(tooltipDataList, options);
    }
    return (
      <>
        <p className={styles.tooltipTitle}>
          {renderName(currentGroup?.graphClassName!)}
        </p>
        {tooltipDataList.slice(0, distribute.reduce((a, b) => a + b, 0)).map((item) => (
          <p className={styles.tooltipDataRow} key={item[labelFieldName]}>
            <span className={styles.tooltipRowName}>{item[labelFieldName]}</span>
            <span className={styles.tooltipData}>
              {renderValue(item[valueFieldName])}
            </span>
          </p>
        ))}
      </>
    );
  };
  return (
    <div className={styles.container}>
      <div className={styles.svgContainer}>
        {detailData.cloudGraphs?.length ? (
          <svg id={svgId} width={width} height={width} style={{ overflow: 'visible' }}></svg>
        ) : (
          EmptyComponent
        )}
      </div>
      {tooltipEnabled &&
        <Tooltip
          ctrlRef={tooltipCtrlRef}
          pointerMargin={tooltipOffset}
          enterable={tooltipEnterable}
          onEnter={() => {
            stateRef.current.tooltipEntered = true;
          }}
          onLeave={() => {
            stateRef.current.tooltipEntered = false;
            stateRef.current.itemLeaveFunc?.();
            stateRef.current.itemLeaveFunc = null;
            stateRef.current.sectorLeaveFunc?.();
            stateRef.current.sectorLeaveFunc = null;
          }}
        >
          {renderTooltipElement}
        </Tooltip>}
    </div>
  );
};

CrystalBall.defaultProps = {
  width: 800,
  EmptyComponent: '暂无数据',
  distribute: [2, 3, 4],
  fieldNames: {
    labelFieldName: 'name',
    valueFieldName: 'tgi',
  },
  tooltipEnabled: true,
  tooltipEnterable: false,
  title: '',
};

export default CrystalBall;
