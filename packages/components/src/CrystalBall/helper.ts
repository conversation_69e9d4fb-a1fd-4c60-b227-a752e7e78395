import seedrandom from 'seedrandom';

export function transformArray(input: number[]): number[] {
  const output: number[] = [];
  const n = input.length;

  // 定义区间边界和偏移量
  const offset = 0.05; // 偏移量，确保值不贴近边界
  const firstInterval = { min: 0.67 + offset, max: 1 - offset }; // (0.67, 1)
  const secondInterval = { min: 0.34 + offset, max: 0.67 - offset }; // (0.34, 0.67)
  const thirdInterval = { min: 0 + offset, max: 0.34 - offset }; // (0, 0.34)

  // 处理第一个区间 (1, 0.67)
  output[0] = firstInterval.max; // 第一个值接近 1，但不等于 1
  output[1] = firstInterval.min; // 第二个值接近 0.67，但不等于 0.67

  // 处理第二个区间 (0.67, 0.34)
  const secondIntervalLength = Math.min(3, n - 2); // 确保不超过数组长度
  for (let i = 2; i < 2 + secondIntervalLength; i++) {
    const ratio = (i - 2) / (secondIntervalLength - 1); // 均匀分布的比例
    output[i] = secondInterval.max - ((secondInterval.max - secondInterval.min) * ratio);
  }

  // 处理第三个区间 (0.34, 0)
  const thirdIntervalLength = n - 5; // 从第6个值到最后一个值
  for (let i = 5; i < n; i++) {
    const ratio = (i - 5) / (thirdIntervalLength - 1); // 均匀分布的比例
    output[i] = thirdInterval.max - ((thirdInterval.max - thirdInterval.min) * ratio);
  }

  // 对第一个值和最后一个值进行额外偏移处理
  output[0] = 1 - offset; // 第一个值接近 1，但不等于 1
  output[n - 1] = 0 + offset; // 最后一个值接近 0，但不等于 0

  return output;
}

export function transformArrayEnhance(input: number[], arr: number[]): number[] {
  const output: number[] = [];

  // 定义区间边界和偏移量
  const offset = 0.05; // 偏移量，确保值不贴近边界
  const firstInterval = { min: 0.67 + offset, max: 1 - offset }; // (0.67, 1)
  const secondInterval = { min: 0.34 + offset, max: 0.67 - offset }; // (0.34, 0.67)
  const thirdInterval = { min: 0 + offset, max: 0.34 - offset }; // (0, 0.34)

  // 获取每个区间的点数
  const [firstCount, secondCount, thirdCount] = arr;

  // 处理第一个区间 (1, 0.67)
  for (let i = 0; i < firstCount; i++) {
    const ratio = i / (firstCount - 1); // 均匀分布的比例
    output.push(firstInterval.max - ((firstInterval.max - firstInterval.min) * ratio));
  }

  // 处理第二个区间 (0.67, 0.34)
  for (let i = 0; i < secondCount; i++) {
    const ratio = i / (secondCount - 1); // 均匀分布的比例
    output.push(secondInterval.max - ((secondInterval.max - secondInterval.min) * ratio));
  }

  // 处理第三个区间 (0.34, 0)
  for (let i = 0; i < thirdCount; i++) {
    const ratio = i / (thirdCount - 1); // 均匀分布的比例
    output.push(thirdInterval.max - ((thirdInterval.max - thirdInterval.min) * ratio));
  }

  // 对第一个值和最后一个值进行额外偏移处理
  if (output.length > 0) {
    output[0] = 1 - (offset * 2); // 第一个值接近 1，但不等于 1
  }
  if (output.length > 1) {
    output[output.length - 1] = 0 + offset; // 最后一个值接近 0，但不等于 0
  }

  return output;
}

// 计算字符串长度（中文算两个长度，英文或符号算一个长度）
export function calculateStringLength(input: string): number {
  let length = 0;
  for (const char of input) {
    if (isChineseCharacter(char)) {
      length += 2; // 中文字符算两个长度
    } else {
      length += 1; // 英文字符或符号算一个长度
    }
  }
  return length;
}

// 判断是否为中文字符
function isChineseCharacter(char: string): boolean {
  return /[\u4e00-\u9fa5]/.test(char);
}

export function customShuffle<T extends unknown[]>(array: T, seed: string) {
  const rng = seedrandom(seed);
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(rng() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

export function groupDataByDistribute<T>(
  list: T[],
  getValue: (item: T) => number, distribute: number[]
) {
  // sort list desc by its value
  const descSortedList = list.slice().sort((a, b) => getValue(b) - getValue(a));
  const grouped: (T | null)[][] = distribute.map((count) => Array(count).fill(null));

  // fill each layer
  let i = 0;
  for (const layer of grouped) {
    let j = 0;
    while (i < descSortedList.length && j < layer.length) {
      layer[j] = descSortedList[i];
      i++;
      j++;
    }
    if (i >= descSortedList.length) {
      break;
    }
  }

  return grouped;
}
