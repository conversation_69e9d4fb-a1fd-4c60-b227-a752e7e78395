import React, { useLayoutEffect, useMemo, useRef, useState } from "react";
import ReactDOM from "react-dom";
import CSSMotion from "rc-motion";
import clsx from "clsx";

import useExpoSmooth from "@ruyi-components/utils/hooks/useExpoSmooth";

import styles from "./index.module.less";

export interface TooltipCtrlRefValue {
  show: (params: { x: number; y: number }) => void;
  hide: () => void;
  update: (params: { x: number; y: number }) => void;
}

export interface TooltipRenderOptions {
  hide: () => void;
}

export const Tooltip = ({
  className,
  style,
  children,
  enterable = true,
  ctrlRef,
  pointerMargin = 16,
  onEnter,
  onLeave,
}: {
  /**
   * tooltip类名
   */
  className?: string;
  /**
   * tooltip样式
   */
  style?: React.CSSProperties;
  /**
   * tooltip内容
   */
  children?: React.ReactNode | ((options: TooltipRenderOptions) => React.ReactNode);
  /**
   * 是否可以鼠标移入操作
   */
  enterable?: boolean;
  /**
   * 距离鼠标的位置偏移
   */
  pointerMargin?: number;
  /**
   * 控制器，通过函数直接控制dom细粒度更新，避免更新state重渲染
   */
  ctrlRef?: React.Ref<TooltipCtrlRefValue>;

  /**
   * 指针进入tooltip的`pointerEnter`回调，仅在 `enterable` 为 true 时生效
   */
  onEnter?: () => void;

  /**
   * 指针离开tooltip的`pointerLeave`回调，仅在 `enterable` 为 true 时生效
   */
  onLeave?: () => void;
}) => {
  // 显隐 tooltip
  const [show, setShow] = useState(false);
  // 触发边缘（白色框以外一圈）进入
  const [triggerEntered, setTriggerEntered] = useState(false);
  // 内容（白色框以内）进入
  // 因为需要在鼠标移出时尽快隐藏tooltip，移入content后要关闭触发边缘
  // 所以需要2组state
  const [contentEntered, setContentEntered] = useState(false);

  const rootRef = useRef<HTMLDivElement | null>(null);

  // 使用指数平滑动画
  const {
    // 设定动画目标
    setValue: tweenTooltipPosition,
    // 帧更新回调
    onFrame: onTooltipPositionUpdate,
    // 动画重置 （避免下次show的时候，从上次hide的位置划过去。而是直接从show的位置出现）
    reset: resetTooltipPosition,
  } = useExpoSmooth<{
    x: number;
    y: number;
  }>({
    rate: 0.2,
  });

  // 将不触发重新渲染的数据放到ref中
  const stateRef = useRef<{
    pointerMargin: number;
    tweenTooltipPosition: typeof tweenTooltipPosition;
    onTooltipPositionUpdate: typeof onTooltipPositionUpdate;
    resetTooltipPosition: typeof resetTooltipPosition;
    refObject: TooltipCtrlRefValue;
    // 在enterable为true时，移到白框外的瞬间，暂时锁定contentEntered状态
    // 使得触发边缘（enterableTrigger）不会立即启用，能够立即关闭白框
    leaveLock: NodeJS.Timeout | null;
    // 可能在初始化设置位置时，rootRef还未挂载，
    // 缓存当前的位置，用于在onEnterPrepare时设置
    cachedPosition: { x: number; y: number } | null;
  }>({
    pointerMargin,
    tweenTooltipPosition,
    onTooltipPositionUpdate,
    resetTooltipPosition,
    refObject: {
      show: (params) => {
        setShow(true);
        stateRef.current.tweenTooltipPosition(params);
      },
      hide: () => {
        setShow(false);
        setTriggerEntered(false);
      },
      update: (params) => {
        stateRef.current.tweenTooltipPosition(params);
      },
    },
    leaveLock: null,
    cachedPosition: null
  });
  // 每次同步最新值
  stateRef.current = {
    ...stateRef.current,
    pointerMargin,
    tweenTooltipPosition,
    onTooltipPositionUpdate,
    resetTooltipPosition,
  };

  // 使用memo以同步方式更新ctrlRef (避免use(Layout)Effect异步)
  useMemo(() => {
    if (!ctrlRef) {
      return;
    }

    if (typeof ctrlRef === "function") {
      ctrlRef(stateRef.current.refObject);
    } else if (typeof ctrlRef === "object" && ctrlRef !== null) {
      (ctrlRef as React.MutableRefObject<TooltipCtrlRefValue>).current =
        stateRef.current.refObject;
    }
  }, [ctrlRef]);

  useLayoutEffect(() => {
    stateRef.current.onTooltipPositionUpdate((v) => {
      if (!rootRef.current) {
        stateRef.current.cachedPosition = v;
        return;
      }

      stateRef.current.cachedPosition = null;
      rootRef.current.style.setProperty(
        "left",
        `${v.x + stateRef.current.pointerMargin}px`
      );
      rootRef.current.style.setProperty(
        "top",
        `${v.y + stateRef.current.pointerMargin}px`
      );
    });
  }, []);

  return ReactDOM.createPortal(
    <CSSMotion
      motionName={{
        enter: styles.tooltipEnter,
        enterActive: styles.tooltipEnterActive,
        leave: styles.tooltipLeave,
        leaveActive: styles.tooltipLeaveActive,
      }}
      visible={show || triggerEntered}
      onEnterPrepare={() => {
        if (!rootRef.current || !stateRef.current.cachedPosition) {
          return;
        }

        const v = stateRef.current.cachedPosition;
        rootRef.current.style.setProperty(
          "left",
          `${v.x + stateRef.current.pointerMargin}px`
        );
        rootRef.current.style.setProperty(
          "top",
          `${v.y + stateRef.current.pointerMargin}px`
        );
        stateRef.current.cachedPosition = null;
      }}
      onLeaveEnd={() => {
        stateRef.current.resetTooltipPosition();
      }}
      ref={(ref) => {
        rootRef.current = ref;
      }}
    >
      {({ style: motionStyle, className: motionClassName }, ref) => {
        return (
          <div
            ref={ref}
            className={clsx(
              styles.tooltip,
              {
                [styles.tooltipEnterable]: enterable,
                [styles.tooltipContentEntered]: contentEntered,
                // [styles.tooltipTriggerEntered]: triggerEntered,
              },
              motionClassName,
              className
            )}
            style={{ ...motionStyle, ...style }}
            onPointerEnter={
              enterable
                ? (e) => {
                  setTriggerEntered(true);
                  e.stopPropagation();

                  onEnter?.();
                }
                : undefined
            }
            onPointerLeave={
              enterable
                ? (e) => {
                  setTriggerEntered(false);
                  e.stopPropagation();

                  onLeave?.();
                }
                : undefined
            }
          >
            {/* 当Enterable为true，在白框外设置一圈触发区域，使得不用hover到白框内也可以保持tooltip */}
            {enterable && (
              <div
                className={styles.tooltipEnterableTrigger}
                style={
                  {
                    "--trigger-margin": pointerMargin * 0.7, /* 暂时写死为和白框和鼠标间距的70% */
                  } as React.CSSProperties
                }
              />
            )}
            <div
              className={styles.tooltipContent}
              onPointerEnter={() => {
                stateRef.current.leaveLock && clearTimeout(stateRef.current.leaveLock)
                setContentEntered(true);

              }}
              onPointerLeave={() => {
                stateRef.current.leaveLock = setTimeout(() => {
                  setContentEntered(false);
                  stateRef.current.leaveLock = null
                }, 500)
              }}
            >
              {typeof children === "function"
                ? children({
                  hide: () => {
                    setShow(false);
                    setTriggerEntered(false);
                    onLeave?.();
                  },
                })
                : children}
            </div>
          </div>
        );
      }}
    </CSSMotion>,
    document.body
  );
};
