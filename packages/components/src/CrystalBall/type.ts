
export interface CloudGrapAudience {
  targetingId: number; // 人群 targetingId
  audienceName: string; // 人群名称
  audienceDesc: string; // 人群描述
  status: 'PROCESSING' | 'SUCCESS' | 'ONLINE' | 'FAIL'; // 状态
  userNum: number; // 用户数量
  createTime: string; // 创建时间
  onlineTime: string; // 推送上线时间
  cloudGraphs: CloudGraphItem[]; // 心云图
  brief: string; // 简要描述
  spuBrief: string; // 简要描述
  spuInfo: {
    spuName: string; // spu名称
    spuCategery: string; // spu品类
    spuType: string; // spu类型
  }[]; // 详细描述
  tags: {
    tagId: number; // 标签id
    tagName: string; // 标签名称
  }[]; // 标签
}

export type CloudGraphItem = {
  graphClass: string; // 类别
  graphClassName: string; // 类别名称
  list: CloudGraphChildItem[];
};

export type CloudGraphChildItem = {
  id: number; // id
  name: string; // 名称
  ta: number; // TA 浓度
  tgi: number; // TGI
};
