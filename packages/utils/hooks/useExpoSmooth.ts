/**
 * 指数平滑动画 hook
 *
 * code by charlieyin
 * adopted from: https://git.woa.com/wxad/adui-charts/blob/master/packages/react/src/utils/use-expo-smooth.ts
 */
import { useCallback, useRef } from 'react';

export default function useExpoSmooth<T extends number | Record<string, number>>(config?: {
  rate?: number;
  minDelta?: number;
}) {
  type onFrameCallback = (result: T) => void;

  const { rate = 0.2, minDelta = 1e-2 } = config || {};

  const onFrameCallbackRef = useRef<onFrameCallback>();
  const onFrame = useCallback((cb: onFrameCallback) => {
    onFrameCallbackRef.current = cb;
  }, []);
  const setValue = useCallback((v: T) => {
    targetValueRef.current = v;
    if (!rafRef.current) {
      triggerChange();
    }
  }, []);
  const currentValueRef = useRef<T>();
  const targetValueRef = useRef<T>();
  const rafRef = useRef<number | null>(null);

  const tween = useCallback((current: T, target: T, changedProps: string[], rate: number) => {
    if (typeof current === 'number' && typeof target === 'number') {
      return rate * target + (1 - rate) * current;
    } else if (typeof current === 'object' && typeof target === 'object' && current !== null && target !== null) {
      changedProps.forEach((prop) => {
        current[prop] = rate * target[prop] + (1 - rate) * current[prop];
      });

      return {
        ...(current as Object),
      };
    }

    // 输入值不合法，直接返回
    return current;
  }, []);

  const reset = useCallback(() => {
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
      (rafRef as React.MutableRefObject<number | null>).current = null;
    }

    currentValueRef.current = undefined;
    targetValueRef.current = undefined;
  }, []);
  const getCurrent = useCallback(() => {
    return currentValueRef.current;
  }, []);

  const triggerChange = useCallback(() => {
    const target = targetValueRef.current;
    const current = currentValueRef.current;

    if (current !== undefined && target !== undefined && current !== target) {
      let changed = false;
      const changedProps: string[] = [];

      if (
        typeof target === 'number' &&
        typeof current === 'number' &&
        Math.abs(target - current) > minDelta
      ) {
        changed = true;
      } else if (typeof target === 'object' && typeof current === 'object' && target !== null && current !== null
      ) {
        Object.keys(target).forEach((key) => {
          if (
            typeof target[key] === 'number' &&
            typeof current[key] === 'number' &&
            Math.abs(target[key] - current[key]) > minDelta
          ) {
            changed = true;
            changedProps.push(key);
          }
        });
      }

      if (changed) {
        rafRef.current = requestAnimationFrame(() => {
          currentValueRef.current = tween(current, target, changedProps, rate) as T;
          onFrameCallbackRef.current?.(currentValueRef.current);
          triggerChange();
        });
      } else {
        if (rafRef.current) {
          cancelAnimationFrame(rafRef.current);
          rafRef.current = null;
          onFrameCallbackRef.current?.(target);
        }
      }
    } else if (current === undefined) {
      // 初始化
      if (typeof targetValueRef.current === 'object' && targetValueRef.current !== null) {
        (currentValueRef.current as Record<string, number>) = {
          ...(targetValueRef.current as Record<string, number>),
        };
      } else if (typeof targetValueRef.current === 'number') {
        currentValueRef.current = targetValueRef.current;
      }

      if (!currentValueRef.current) {
        return;
      }
      // 初始化的时候需要响应一次
      onFrameCallbackRef.current?.(currentValueRef.current);
    }
  }, [rate]);

  return {
    onFrame,
    setValue,
    reset,
    getCurrent,
  };
}
