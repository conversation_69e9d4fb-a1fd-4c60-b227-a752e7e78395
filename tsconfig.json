{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "outDir": "./dist", "baseUrl": "./", "paths": {"@components/*": ["packages/components/src/*"], "@ruyi-components/components/*": ["packages/components/src/*"], "@ruyi-components/utils/*": ["packages/utils/src/*"], "@utils/*": ["packages/utils/src/*"]}}, "include": ["packages/**/*"], "exclude": ["node_modules", "dist"]}