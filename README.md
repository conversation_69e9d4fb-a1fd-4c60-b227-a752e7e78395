# Ruyi Components

A modern React component library built with TypeScript, featuring interactive data visualization components and utility hooks.

## 🚀 Features

- **Modern React Components**: Built with React 17+ and TypeScript
- **Interactive Data Visualization**: Advanced components like CrystalBall for data representation
- **Utility Hooks**: Custom hooks for smooth animations and interactions
- **Storybook Documentation**: Interactive component documentation and examples
- **Monorepo Structure**: Organized with separate packages for components and utilities
- **TypeScript Support**: Full TypeScript support with type definitions
- **Less/CSS Modules**: Styled with Less and CSS modules for scoped styling

## 📦 Packages

This is a monorepo containing the following packages:

### @ruyi-components/components

The main component library containing:

- **Button**: Customizable button component with multiple themes and sizes
- **CrystalBall**: Advanced data visualization component with interactive tooltips and D3.js integration

### @ruyi-components/utils

Utility package containing:

- **useExpoSmooth**: Hook for exponential smoothing animations

## 🛠 Installation

```bash
# Install with pnpm (recommended)
pnpm install ruyi-components

# Or with npm
npm install ruyi-components

# Or with yarn
yarn add ruyi-components
```

## 📖 Usage

### Button Component

```tsx
import { Button } from "ruyi-components";

function App() {
  return (
    <div>
      <Button
        label="Primary Button"
        theme="primary"
        size="medium"
        onClick={() => console.log("Clicked!")}
      />
      <Button label="Secondary Button" theme="secondary" size="large" />
    </div>
  );
}
```

#### Button Props

| Prop        | Type                             | Default     | Description            |
| ----------- | -------------------------------- | ----------- | ---------------------- |
| `label`     | `string`                         | -           | Button text content    |
| `onClick`   | `() => void`                     | -           | Click handler function |
| `theme`     | `'primary' \| 'secondary'`       | `'primary'` | Button theme/style     |
| `size`      | `'small' \| 'medium' \| 'large'` | `'small'`   | Button size            |
| `className` | `string`                         | -           | Additional CSS class   |
| `style`     | `CSSProperties`                  | -           | Inline styles          |

### CrystalBall Component

```tsx
import { CrystalBall } from "ruyi-components";

const data = [
  { name: "Group A", value: 100 },
  { name: "Group B", value: 200 },
  { name: "Group C", value: 150 },
];

function App() {
  return (
    <CrystalBall
      title="Data Visualization"
      data={data}
      width={800}
      tooltipEnabled={true}
      formatValue={(value) => `${value} users`}
    />
  );
}
```

#### CrystalBall Props

| Prop                 | Type                           | Default        | Description                           |
| -------------------- | ------------------------------ | -------------- | ------------------------------------- |
| `title`              | `string`                       | -              | Chart title                           |
| `data`               | `any[]`                        | -              | Data array for visualization          |
| `width`              | `number`                       | `800`          | Chart width in pixels                 |
| `svgId`              | `string`                       | auto-generated | SVG element ID                        |
| `distribute`         | `number[]`                     | `[2, 3, 4]`    | Distribution pattern for layers       |
| `EmptyComponent`     | `ReactNode`                    | `"暂无数据"`   | Component shown when no data          |
| `fieldNames`         | `object`                       | -              | Field mapping for data properties     |
| `formatTooltipTitle` | `(name: string) => ReactNode`  | -              | Tooltip title formatter               |
| `formatValue`        | `(value: number) => ReactNode` | -              | Value formatter                       |
| `renderTooltip`      | `function`                     | -              | Custom tooltip renderer               |
| `tooltipEnabled`     | `boolean`                      | -              | Enable/disable tooltips               |
| `tooltipEnterable`   | `boolean`                      | -              | Allow mouse interaction with tooltips |

### Utility Hooks

```tsx
import { useExpoSmooth } from "@ruyi-components/utils/hooks/useExpoSmooth";

function AnimatedComponent() {
  const { setValue, onFrame, reset } = useExpoSmooth<{ x: number; y: number }>({
    rate: 0.2,
    minDelta: 1e-2,
  });

  onFrame((position) => {
    // Update component position with smooth animation
    console.log("Animated position:", position);
  });

  const handleMove = (x: number, y: number) => {
    setValue({ x, y });
  };

  return <div>{/* Your animated content */}</div>;
}
```

## 🏗 Development

### Prerequisites

- Node.js 16+
- pnpm (recommended package manager)

### Setup

```bash
# Clone the repository
git clone <repository-url>
cd ruyi-components

# Install dependencies
pnpm install

# Build all packages
pnpm build

# Start Storybook for development
cd docs
pnpm storybook
```

### Project Structure

```
ruyi-components/
├── packages/
│   ├── components/          # Main component library
│   │   ├── src/
│   │   │   ├── Button/      # Button component
│   │   │   └── CrystalBall/ # CrystalBall component
│   │   └── package.json
│   └── utils/               # Utility functions and hooks
│       ├── hooks/
│       │   └── useExpoSmooth.ts
│       └── package.json
├── docs/                    # Storybook documentation
├── package.json             # Root package.json
├── pnpm-workspace.yaml      # Workspace configuration
└── README.md
```

### Available Scripts

```bash
# Build all packages
pnpm build

# Clean build artifacts
pnpm clean

# Start Storybook development server
cd docs && pnpm storybook

# Build Storybook for production
cd docs && pnpm build-storybook
```

## 🧪 Testing

The project uses Storybook for component testing and documentation. You can run the Storybook development server to test components interactively:

```bash
cd docs
pnpm storybook
```

## 📚 Documentation

Component documentation is available through Storybook. After starting the development server, you can:

- View interactive component examples
- Test different component props
- See component documentation and usage guidelines

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the ISC License.

## 🔧 Technical Details

### Dependencies

#### Core Dependencies

- **React**: ^17.0.0 - UI library
- **React DOM**: ^17.0.0 - DOM rendering

#### Component Dependencies

- **D3.js**: ^7.9.0 - Data visualization
- **ahooks**: ^3.8.5 - React hooks library
- **clsx**: ^2.1.1 - Conditional CSS classes
- **lodash-es**: ^4.17.21 - Utility functions
- **nanoid**: ^5.1.5 - Unique ID generation
- **numeral**: ^2.0.6 - Number formatting
- **rc-motion**: ^2.9.5 - Animation library
- **seedrandom**: ^3.0.5 - Seeded random number generation

#### Development Dependencies

- **TypeScript**: ^5.8.3 - Type checking
- **Vite**: ^6.3.5 - Build tool
- **Less**: ^4.3.0 - CSS preprocessor
- **Storybook**: ^9.0.6 - Component documentation

### Build Configuration

The project uses Vite for building with the following configuration:

- Library mode for component packaging
- TypeScript declaration generation
- External React dependencies
- CSS modules with Less preprocessing

---

Made with ❤️ by the Ruyi Components team
